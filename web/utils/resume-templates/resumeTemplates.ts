/**
 * Resume template collection
 * Contains predefined HTML templates for resumes
 */

export interface ResumeTemplate {
  id: string;
  name: string;
  previewDescription: string;
  recommended: boolean;
  tokenCost: number;
  fontFamily: string;
}

export const cleanProfessionalTemplate: ResumeTemplate = {
    id: "clean-professional",
    name: "Clean Professional",
    previewDescription: "Template resume bersih dan profesional untuk semua industri",
    tokenCost: 15,
    recommended: true,
    fontFamily: 'Arial, sans-serif',
};

export const modernCleanTemplate: ResumeTemplate = {
    id: "modern-clean",
    name: "Modern Clean",
    previewDescription: "Template resume minimalis dengan desain modern dan elegan untuk profesional",
    tokenCost: 15,
    recommended: false,
    fontFamily: 'Arial, sans-serif',
};

export const classicProfessionalTemplate: ResumeTemplate = {
    id: "classic-professional",
    name: "Classic Professional",
    previewDescription: "Template resume klasik dengan format standar yang mudah dibaca dan maks<PERSON>lk<PERSON> peluang lolos screening otomatis",
    tokenCost: 15,
    recommended: true,
    fontFamily: 'Arial, sans-serif',
};

export const minimalistProfessionalTemplate: ResumeTemplate = {
    id: "minimalist-professional",
    name: "Minimalist Professional",
    previewDescription: "Template resume minimalis dengan desain bersih dan elegan untuk profesional yang mengutamakan kesederhanaan",
    tokenCost: 15,
    recommended: false,
    fontFamily: 'Arial, sans-serif',
};

export const executiveProfessionalTemplate: ResumeTemplate = {
    id: "executive-professional",
    name: "Executive Professional",
    previewDescription: "Template resume eksekutif dengan desain profesional dan layout yang kuat untuk posisi senior dan manajemen",
    tokenCost: 15,
    recommended: true,
    fontFamily: 'Arial, sans-serif',
};

// Collection of all available templates
export const resumeTemplates: ResumeTemplate[] = [
    cleanProfessionalTemplate,
    modernCleanTemplate,
    classicProfessionalTemplate,
    minimalistProfessionalTemplate,
    executiveProfessionalTemplate,
];

// Get template by ID
export function getResumeTemplateById(id: string): ResumeTemplate | undefined {
    return resumeTemplates.find(template => template.id === id);
}

// Get all templates
export function getAllResumeTemplates(): ResumeTemplate[] {
    return resumeTemplates;
}